import askMeAnythingIcon from "@/assets/icons/ask-me-anything.svg";
import deepSearchIcon from "@/assets/icons/deep-search.svg";
import studyGuideIcon from "@/assets/icons/study-guide.svg";
import voiceIcon from "@/assets/icons/voice.svg";
import Image from "next/image";
import Link from "next/link";

const AskRabbai = () => {
  return (
    <div className="flex flex-col px-4 md:gap-[6rem] md:px-8 lg:flex-row lg:px-14 xl:gap-[10rem]">
      <div className="flex flex-col items-center gap-[5rem] lg:w-[30%]">
        <div className="text-4xl font-bold lg:text-5xl xl:text-[3.5rem]">
          <span>Ask Rabb</span>
          <span className="text-primary">AI</span>
        </div>
        <div className="mb-20 flex h-[20rem] w-[20rem] items-center justify-center rounded-full bg-gradient-to-b from-[#FFFFFFB8] to-[#FFFFFF0A]">
          <div className="flex h-[14.5rem] w-[14.5rem] flex-col items-center rounded-full bg-[linear-gradient(to_bottom,#390046,#FF6B9557)]">
            <div className="mt-14 mb-2">
              <span className="text-sm font-light text-[#FFFFFFB0]">Hi, Michael </span>
              <span>👋</span>
            </div>
            <p className="text-xl font-bold">Tap to Ask RabbAI</p>
            <div className="mt-4">
              <Image src={voiceIcon} alt="Voice Icon" className="h-[3.125rem] w-[3.125rem]" />
            </div>
          </div>
        </div>
      </div>
      <div className="lg:w-[70%] lg:py-3 xl:py-6">
        <p className="mb-[2rem] text-2xl font-bold">Quick Start</p>
        <div className="grid gap-x-3 gap-y-6 md:grid-cols-1 lg:grid-cols-2">
          <Link
            href="/rabbai/deep-search"
            className="relative flex h-[8rem] items-center gap-4 rounded-2xl bg-gradient-to-b from-[#FFFFFF3B] to-[#B88C0038] p-[1px] md:h-[12rem] md:flex-col md:items-start md:justify-between md:rounded-3xl lg:h-[16rem]"
          >
            <div className="flex h-full w-full items-center gap-4 rounded-2xl bg-[#FFFFFF0A] p-5 md:flex-col md:items-start md:justify-between md:rounded-3xl">
              <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
                <Image src={deepSearchIcon} alt="Deep Search Icon" width={24} height={24} />
              </div>
              <div>
                <p className="text-[1.25rem] font-semibold">Deep Search</p>
                <p className="mt-3 font-light text-[#FFFFFFB0]">Keyword to find messages related to your search</p>
              </div>
            </div>
          </Link>
          <Link
            href="/rabbai/study-guide"
            className="relative flex h-[8rem] items-center gap-4 rounded-2xl bg-gradient-to-b from-[#FFFFFF3B] to-[#B88C0038] p-[1px] md:h-[12rem] md:flex-col md:items-start md:justify-between md:rounded-3xl lg:h-[16rem]"
          >
            <div className="flex h-full w-full items-center gap-4 rounded-2xl bg-[#FFFFFF0A] p-5 md:flex-col md:items-start md:justify-between md:rounded-3xl">
              <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
                <Image src={studyGuideIcon} alt="Study Guide Icon" width={24} height={24} />
              </div>
              <div>
                <p className="text-[1.25rem] font-semibold">Study Guide</p>
                <p className="mt-3 font-light text-[#FFFFFFB0]">Prepare a study guide on a particular topic</p>
              </div>
            </div>
          </Link>
          <Link
            href="/rabbai/ask-me-anything"
            className="relative flex h-[8rem] items-center gap-4 rounded-2xl bg-gradient-to-b from-[#FFFFFF3B] to-[#B88C0038] p-[1px] md:h-[12rem] md:flex-col md:items-start md:justify-between md:rounded-3xl lg:h-[16rem]"
          >
            <div className="flex h-full w-full items-center gap-4 rounded-2xl bg-[#FFFFFF0A] p-5 md:flex-col md:items-start md:justify-between md:rounded-3xl">
              <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
                <Image src={askMeAnythingIcon} alt="Ask Me Anything Icon" width={24} height={24} />
              </div>
              <div>
                <p className="text-[1.25rem] font-semibold">Ask me Anything</p>
                <p className="mt-3 font-light text-[#FFFFFFB0]">
                  An "Ask Me Anything" chatbot that responds exclusively using content from Fountain Stream.
                </p>
              </div>
            </div>
            <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
              <Image src={deepSearchIcon} alt="Deep Search Icon" width={24} height={24} />
            </div>
            <div>
              <p className="text-[1.25rem] font-semibold">Deep Search</p>
              <p className="mt-3 font-light text-[#FFFFFFB0]">Keyword to find messages related to your search</p>
            </div>
          </Link>
          <Link
            href="/rabbai/study-guide"
            className="flex h-[8rem] items-center gap-4 rounded-2xl border border-[#FFFFFF3B] bg-[#FFFFFF0A] p-5 md:h-[12rem] md:flex-col md:items-start md:justify-between md:rounded-3xl lg:h-[16rem]"
          >
            <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
              <Image src={studyGuideIcon} alt="Study Guide Icon" width={24} height={24} />
            </div>
            <div>
              <p className="text-[1.25rem] font-semibold">Study Guide</p>
              <p className="mt-3 font-light text-[#FFFFFFB0]">Prepare a study guide on a particular topic</p>
            </div>
          </Link>
          <Link
            href="/rabbai/ask-me-anything"
            className="flex h-[8rem] items-center gap-4 rounded-2xl border border-[#FFFFFF3B] bg-[#FFFFFF0A] p-5 md:h-[12rem] md:flex-col md:items-start md:justify-between md:rounded-3xl lg:h-[16rem]"
          >
            <div className="flex size-12 shrink-0 items-center justify-center rounded-[6px] bg-[#FFFFFF1A]">
              <Image src={askMeAnythingIcon} alt="Ask Me Anything Icon" width={24} height={24} />
            </div>
            <div>
              <p className="text-[1.25rem] font-semibold">Ask me Anything</p>
              <p className="mt-3 font-light text-[#FFFFFFB0]">
                An "Ask Me Anything" chatbot that responds exclusively using content from Fountain Stream.
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AskRabbai;

"use client";
import ConversationHistory from "@/components/rabbai/ConversationHistory";
import { studyGuideConversations } from "@/data/rabbai";
import { useState } from "react";
import { HiMenuAlt2 } from "react-icons/hi";

const StudyGuideLayout = ({ children }: { children: React.ReactNode }) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(false);
  return (
    <div>
      <ConversationHistory conversations={studyGuideConversations} isHistoryOpen={isHistoryOpen} setIsHistoryOpen={setIsHistoryOpen} />
      <HiMenuAlt2 size={24} onClick={() => setIsHistoryOpen(true)} />
      <div className="flex-1 px-4 lg:ml-[25rem] lg:px-0">{children}</div>
    </div>
  );
};

export default StudyGuideLayout;

"use client";
import NavLink from "@/components/common/NavLink";
import HomeIcon from "@/components/icons/Home";
import LibraryIcon from "@/components/icons/Library";
import MeetingsIcon from "@/components/icons/Meetings";
import MessagesIcon from "@/components/icons/Messages";
import RabbaiIcon from "@/components/icons/Rabbai";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

const navLinks = [
  {
    icon: HomeIcon,
    href: "/home",
    name: "Home",
  },
  {
    icon: RabbaiIcon,
    href: "/rabbai",
    name: "RabbAI",
  },
  {
    icon: MessagesIcon,
    href: "/messages",
    name: "Messages",
  },
  {
    icon: MeetingsIcon,
    href: "/meetings",
    name: "Meetings",
  },
  {
    icon: LibraryIcon,
    href: "/library",
    name: "Library",
  },
];

const BottomNavbar = () => {
  const pathname = usePathname();
  const paths = pathname.split("/");

  return (
    <div className="fixed bottom-0 flex h-(--nav-height) w-full bg-[#020213] lg:hidden">
      <nav className="flex w-full justify-between px-5 py-2">
        {navLinks.map((link, i) => {
          const Icon = link.icon;
          const isActive = paths[1] === String(link.href).slice(1);
          return (
            <NavLink key={i} href={link.href} className="flex flex-col justify-center gap-1.5 px-8">
              <Icon isActive={isActive} className="flex-1" />
              <p className={cn("py-0 text-xs text-[#676D75]", isActive && "text-white")}>{link.name}</p>
            </NavLink>
          );
        })}
      </nav>
    </div>
  );
};

export default BottomNavbar;

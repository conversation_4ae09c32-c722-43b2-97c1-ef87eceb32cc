import { studyGuideSchema } from "@/schemas/rabbai";
import { StaticImageData } from "next/image";
import { z } from "zod";

export type Short = {
  id: number;
  title: string;
  image: StaticImageData;
  duration: string;
  numberOfListens: number;
  date: string;
};

export type Update = {
  id: number;
  title: string;
  details: string;
  ministry: string;
  image: StaticImageData;
};

export type NewbieMessage = {
  id: number;
  title: string;
  image: StaticImageData;
  count: number;
};

export type Release = {
  id: number;
  messageTitle: string;
  image: StaticImageData;
  date: string;
  minister: {
    name: string;
    image: StaticImageData;
  };
  duration: string;
};

export type RabbaiConversation = {
  id: number;
  date: string;
  messages: {
    id: number;
    title: string;
    created_at: string;
  }[];
};

export type StudyGuideFormData = z.infer<typeof studyGuideSchema>;

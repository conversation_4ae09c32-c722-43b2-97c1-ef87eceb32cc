import backIcon from "@/assets/icons/arrow-left.svg";
import rabbaiConversationsIcon from "@/assets/icons/rabbai-conversations.svg";
import SearchInput from "@/components/common/SearchInput";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ConversationHistoryProps } from "./types";

const ConversationHistory = ({ conversations, isHistoryOpen, setIsHistoryOpen, route }: ConversationHistoryProps) => {
  const router = useRouter();
  return (
    <div
      className={`lg:scrollbar-thin lg:scrollbar-track-transparent lg:scrollbar-thumb-white/30 lg:hover:scrollbar-thumb-white/50 bg-secondary/95 fixed top-20 left-0 z-10 max-h-[85dvh] w-dvw overflow-y-auto p-[2rem] backdrop-blur-sm transition-transform duration-300 ease-in-out lg:left-[5rem] lg:w-[25rem] lg:translate-x-0 lg:border-r lg:border-[#D0D5DD29] lg:bg-transparent lg:backdrop-blur-none xl:w-[27rem] ${isHistoryOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/rabbai" className="hidden lg:block">
            <Image src={backIcon} alt="Conversation Icon" width={22} height={22} />
          </Link>
          <Button onClick={() => setIsHistoryOpen(false)} className="size-10 rounded-full bg-white/10 transition-colors hover:bg-white/20 lg:hidden">
            <X size={16} className="text-white" />
          </Button>
          <p>Ask RabbAI</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="grid size-12 place-items-center rounded-full bg-white">
            <Image src={rabbaiConversationsIcon} alt="Conversation Icon" width={22} height={22} />
          </div>
        </div>
      </div>
      <div className="mt-6">
        <SearchInput className="w-full rounded-[50px]" placeholder="Search Conversation" />
        <div className="mt-6 space-y-6">
          {conversations.map(conversation => (
            <div key={conversation.id} className="cursor-pointer" onClick={() => router.push(`${route}/${conversation.id}`)}>
              <p>{conversation.date}</p>
              <div className="mt-2 space-y-4">
                {conversation.messages.map(message => (
                  <div className="h-[3.4rem] rounded-[6px] bg-[#FFFFFF0F] px-4 py-2" key={message.id}>
                    <p className="truncate text-[0.875rem]">{message.title}</p>
                    <p className="text-[0.75rem] font-light text-[#FFFFFF73]">{message.created_at}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConversationHistory;
